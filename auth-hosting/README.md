# Firebase Auth Hosting

This directory contains the minimal hosting setup for Firebase Authentication using a custom domain.

## Purpose

This Firebase Hosting setup serves as a dedicated authentication domain that allows:

- Custom branding in Google OAuth popups (shows your domain instead of Firebase's default domain)
- Separation of auth services from the main application (hosted on Vercel)
- Security isolation for authentication flows

## How It Works

Firebase Auth automatically provides these endpoints:

- `/__/auth/handler` - OAuth redirect handler
- `/__/auth/iframe` - Auth iframe for popups
- Built-in Firebase auth infrastructure

**No custom HTML files are required** - Firebase handles all authentication logic automatically.

## Current Setup

- **Hosting URL**: `https://brotrip-mvp.web.app`
- **Custom Domain**: Can be configured to `https://mydomain.com`
- **Main App**: Hosted separately on Vercel
- **Auth Domain**: Used only for OAuth flows

## Configuration

### 1. Firebase Hosting (firebase.json)

```json
{
  "hosting": {
    "public": "auth-hosting",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]
  }
}
```

### 2. Main App Firebase Config

```javascript
const firebaseConfig = {
  authDomain: "brotrip-mvp.web.app", // or your custom domain
  // ... other config
}
```

### 3. Google OAuth Settings

Add to Google Cloud Console OAuth 2.0 Client:

- **Authorized JavaScript origins**: `https://brotrip-mvp.web.app`
- **Authorized redirect URIs**: `https://brotrip-mvp.web.app/__/auth/handler`

## Security Notes

- ✅ No Firebase configuration exposed in static files
- ✅ Firebase handles all auth logic securely
- ✅ Domain ownership verified through Firebase Hosting
- ✅ OAuth flows isolated from main application

## Deployment

```bash
# Deploy auth hosting
firebase deploy --only hosting

# Deploy to specific project
firebase use local  # or prod
firebase deploy --only hosting
```

## Custom Domain Setup (Optional)

To use `mydomain.com`:

1. Add custom domain in Firebase Console
2. Configure DNS records as instructed
3. Update `authDomain` in main app config
4. Add custom domain to Google OAuth settings

## Files

- `404.html` - Simple placeholder page (not used for auth flows)
- `README.md` - This documentation

## Important

The actual authentication happens through Firebase's built-in endpoints, not through any custom HTML files in this directory. This hosting setup is purely for domain ownership and branding purposes.
