<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Togeda.ai Authentication</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        margin: 0;
        background: linear-gradient(135deg, #00796b, #ffd54f);
      }
      .container {
        text-align: center;
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        max-width: 400px;
      }
      .logo {
        font-size: 2rem;
        font-weight: bold;
        color: #00796b;
        margin-bottom: 1rem;
      }
      .message {
        color: #666;
        margin-bottom: 1.5rem;
      }
      .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #00796b;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">Togeda.ai</div>
      <div class="message">Completing authentication...</div>
      <div class="spinner"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
      // This page handles Firebase Auth redirects
      // Firebase will automatically handle the auth flow
      import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js"
      import { getAuth } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"

      const firebaseConfig = {
        apiKey: "AIzaSyCqa9BpCNRO5TTlixOfNM95uUTyitJ25TA",
        authDomain: "auth.yourdomain.com", // Replace with your actual auth subdomain
        projectId: "brotrip-d7c58",
        storageBucket: "brotrip-d7c58.firebasestorage.app",
        messagingSenderId: "459512791413",
        appId: "1:459512791413:web:003c4500481382417f133c",
      }

      const app = initializeApp(firebaseConfig)
      const auth = getAuth(app)

      // Firebase Auth will handle the redirect automatically
      console.log("Firebase Auth initialized for redirect handling")
    </script>
  </body>
</html>
